# Rachnak

**Rachnak – Create, Track, and Reward Your Day.**  
A smart productivity app built with Flutter that helps users manage expenses, to-dos, dates, and earn coins to redeem products.

---

## 🌟 Features

- ✅ **Daily To-Dos** – Stay organized with a personalized daily checklist.
- 💰 **Expense Tracker** – Keep track of your daily spending.
- 📅 **Date Saver** – Paste and store important dates for quick access.
- 🎁 **Coin System** – Earn coins through daily check-ins and use them to redeem products.
- 🛒 **Product Store** – Browse and buy products using earned coins.
- 📊 **Activity Tracker** – View your activity history to stay on top of your progress.
- 🌐 **Multilingual Support** – Use the app in your preferred language.
- 🎨 **Theme** – Clean and calming light green & white UI.

---

## 🚀 Getting Started

### Prerequisites

- Flutter SDK (>=3.0.0)
- Dart (>=2.17.0)
- Android Studio or VS Code
- Internet connection

### Installation

```bash
git clone https://github.com/yourusername/rachnak.git
cd rachnak
flutter pub get
flutter run
