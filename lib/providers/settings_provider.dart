import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:devicelocale/devicelocale.dart';
import 'dart:ui' as ui;

class SettingsProvider extends ChangeNotifier {
  bool _isHindi = false;
  bool _isDailyReminderEnabled = false;
  TimeOfDay _reminderTime = const TimeOfDay(hour: 20, minute: 0);
  ThemeMode _themeMode = ThemeMode.system;
  bool _hasDetectedLanguage = false;

  SettingsProvider() {
    _loadSettings();
  }
  
  // Getters
  bool get isHindi => _isHindi;
  bool get isDailyReminderEnabled => _isDailyReminderEnabled;
  TimeOfDay get reminderTime => _reminderTime;
  ThemeMode get themeMode => _themeMode;
  
  // Toggle language between English and Hindi
  Future<void> toggleLanguage() async {
    _isHindi = !_isHindi;
    await _saveSettings();
    notifyListeners();
  }
  
  // Set daily reminder enabled/disabled (keep this for UI purposes)
  Future<void> setDailyReminder(bool enabled) async {
    _isDailyReminderEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }
  
  // Set reminder time (keep this for UI purposes)
  Future<void> setReminderTime(TimeOfDay time) async {
    _reminderTime = time;
    await _saveSettings();
    notifyListeners();
  }
  
  // Set theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    await _saveSettings();
    notifyListeners();
  }

  // Detect and set device language automatically
  Future<void> detectAndSetDeviceLanguage() async {
    if (_hasDetectedLanguage) return; // Only detect once

    try {
      // Try to get device locale
      String? currentLocale = await Devicelocale.currentLocale;

      if (currentLocale != null) {
        // Check if device language is Hindi or region is India
        bool shouldUseHindi = currentLocale.startsWith('hi') ||
                             currentLocale.contains('IN');

        if (shouldUseHindi != _isHindi) {
          _isHindi = shouldUseHindi;
          _hasDetectedLanguage = true;
          await _saveSettings();
          notifyListeners();
        }
      } else {
        // Fallback to system locale
        final systemLocale = ui.PlatformDispatcher.instance.locale;
        bool shouldUseHindi = systemLocale.languageCode == 'hi' ||
                             systemLocale.countryCode == 'IN';

        if (shouldUseHindi != _isHindi) {
          _isHindi = shouldUseHindi;
          _hasDetectedLanguage = true;
          await _saveSettings();
          notifyListeners();
        }
      }
    } catch (e) {
      // If detection fails, use system locale as fallback
      final systemLocale = ui.PlatformDispatcher.instance.locale;
      bool shouldUseHindi = systemLocale.languageCode == 'hi' ||
                           systemLocale.countryCode == 'IN';

      if (shouldUseHindi != _isHindi) {
        _isHindi = shouldUseHindi;
        _hasDetectedLanguage = true;
        await _saveSettings();
        notifyListeners();
      }
    }
  }
  
  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    _isHindi = prefs.getBool('isHindi') ?? false;
    _isDailyReminderEnabled = prefs.getBool('isDailyReminderEnabled') ?? false;
    _hasDetectedLanguage = prefs.getBool('hasDetectedLanguage') ?? false;

    final reminderHour = prefs.getInt('reminderHour') ?? 20;
    final reminderMinute = prefs.getInt('reminderMinute') ?? 0;
    _reminderTime = TimeOfDay(hour: reminderHour, minute: reminderMinute);

    final themeModeIndex = prefs.getInt('themeMode') ?? 0;
    _themeMode = ThemeMode.values[themeModeIndex];

    notifyListeners();

    // Auto-detect language on first run
    if (!_hasDetectedLanguage) {
      await detectAndSetDeviceLanguage();
    }
  }
  
  // Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setBool('isHindi', _isHindi);
    await prefs.setBool('isDailyReminderEnabled', _isDailyReminderEnabled);
    await prefs.setBool('hasDetectedLanguage', _hasDetectedLanguage);
    await prefs.setInt('reminderHour', _reminderTime.hour);
    await prefs.setInt('reminderMinute', _reminderTime.minute);
    await prefs.setInt('themeMode', _themeMode.index);
  }
}
