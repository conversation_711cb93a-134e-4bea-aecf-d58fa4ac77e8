package com.rahulkatre.rupify

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugins.GeneratedPluginRegistrant
import android.os.Bundle

class MainActivity : FlutterActivity() {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Ensure the app can receive notifications when in background
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(android.content.Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

            // Create notification channels if they don't exist
            val todoChannel = android.app.NotificationChannel(
                "todo_channel",
                "Todo Reminders",
                android.app.NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for todo reminders"
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
                lightColor = android.graphics.Color.parseColor("#36916D")
            }

            val confirmationChannel = android.app.NotificationChannel(
                "todo_confirmation_channel",
                "Todo Confirmations",
                android.app.NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Immediate confirmations when todos are scheduled"
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
                lightColor = android.graphics.Color.parseColor("#36916D")
            }

            notificationManager.createNotificationChannel(todoChannel)
            notificationManager.createNotificationChannel(confirmationChannel)
        }
    }
}
