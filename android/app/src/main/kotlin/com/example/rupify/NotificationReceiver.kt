package com.rahulkatre.rupify

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.app.NotificationManager
import android.app.NotificationChannel
import android.os.Build

class NotificationReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        // This receiver ensures notification channels are created even when app is closed
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // Create notification channels
            val todoChannel = NotificationChannel(
                "todo_channel",
                "Todo Reminders",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for todo reminders"
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
                lightColor = android.graphics.Color.parseColor("#36916D")
            }
            
            val confirmationChannel = NotificationChannel(
                "todo_confirmation_channel",
                "Todo Confirmations", 
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Immediate confirmations when todos are scheduled"
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
                lightColor = android.graphics.Color.parseColor("#36916D")
            }
            
            notificationManager.createNotificationChannel(todoChannel)
            notificationManager.createNotificationChannel(confirmationChannel)
        }
    }
}
