import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rupify/main.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/providers/todo_provider.dart';
import 'package:rupify/utils/notification_service.dart';

void main() {
  group('Notification Integration Tests', () {
    testWidgets('App should initialize notification service on startup', (WidgetTester tester) async {
      // Initialize Flutter binding
      TestWidgetsFlutterBinding.ensureInitialized();
      
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({
        'isHindi': false,
        'hasDetectedLanguage': false,
        'isDailyReminderEnabled': false,
        'reminderHour': 20,
        'reminderMinute': 0,
        'themeMode': 0,
      });

      // Build the app
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => SettingsProvider()),
            ChangeNotifierProvider(create: (_) => TodoProvider()),
          ],
          child: const MyApp(),
        ),
      );

      // Wait for the app to settle
      await tester.pumpAndSettle();

      // Verify that the app loads without errors
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('Settings provider should detect device language', (WidgetTester tester) async {
      TestWidgetsFlutterBinding.ensureInitialized();
      
      // Mock SharedPreferences with no previous language detection
      SharedPreferences.setMockInitialValues({
        'isHindi': false,
        'hasDetectedLanguage': false,
        'isDailyReminderEnabled': false,
        'reminderHour': 20,
        'reminderMinute': 0,
        'themeMode': 0,
      });

      final settingsProvider = SettingsProvider();
      
      // Wait for initialization
      await tester.pump();
      
      // Verify that the settings provider is initialized
      expect(settingsProvider, isNotNull);
      
      // The language detection should have been attempted
      // (In a real test, we would mock the device locale detection)
    });

    testWidgets('Todo creation should trigger notification scheduling', (WidgetTester tester) async {
      TestWidgetsFlutterBinding.ensureInitialized();
      
      SharedPreferences.setMockInitialValues({
        'isHindi': false,
        'hasDetectedLanguage': true,
        'isDailyReminderEnabled': false,
        'reminderHour': 20,
        'reminderMinute': 0,
        'themeMode': 0,
      });

      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => SettingsProvider()),
            ChangeNotifierProvider(create: (_) => TodoProvider()),
          ],
          child: const MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Find the add todo button (assuming it exists in the UI)
      final addButton = find.byIcon(Icons.add);
      if (addButton.evaluate().isNotEmpty) {
        await tester.tap(addButton);
        await tester.pumpAndSettle();
        
        // This would test the actual todo creation flow
        // In a real integration test, we would fill out the form and submit
      }
      
      // Verify no errors occurred
      expect(tester.takeException(), isNull);
    });
  });

  group('Notification Service Unit Tests', () {
    test('should handle timezone initialization', () async {
      TestWidgetsFlutterBinding.ensureInitialized();
      
      final notificationService = NotificationService();
      
      // Test that the service can be created without errors
      expect(notificationService, isNotNull);
    });

    test('should generate correct notification IDs', () {
      // Test ID generation logic
      const baseId = '123';
      final confirmationId = int.parse(baseId) * 100;
      final fifteenMinId = int.parse(baseId) * 15;
      final fiveMinId = int.parse(baseId) * 5;
      
      expect(confirmationId, equals(12300));
      expect(fifteenMinId, equals(1845));
      expect(fiveMinId, equals(615));
      
      // Ensure IDs are unique
      expect(confirmationId != fifteenMinId, isTrue);
      expect(fifteenMinId != fiveMinId, isTrue);
      expect(fiveMinId != int.parse(baseId), isTrue);
    });

    test('should handle language-specific notification content', () {
      // Test Hindi content
      const isHindi = true;
      final hindiTitle = isHindi ? 'कार्य निर्धारित' : 'Todo Scheduled';
      final hindiReminder = isHindi ? 'आपका कार्य 15 मिनट में शुरू होगा' : 'Your todo starts in 15 minutes';
      
      expect(hindiTitle, equals('कार्य निर्धारित'));
      expect(hindiReminder, equals('आपका कार्य 15 मिनट में शुरू होगा'));
      
      // Test English content
      const isEnglish = false;
      final englishTitle = isEnglish ? 'कार्य निर्धारित' : 'Todo Scheduled';
      final englishReminder = isEnglish ? 'आपका कार्य 15 मिनट में शुरू होगा' : 'Your todo starts in 15 minutes';
      
      expect(englishTitle, equals('Todo Scheduled'));
      expect(englishReminder, equals('Your todo starts in 15 minutes'));
    });
  });
}
