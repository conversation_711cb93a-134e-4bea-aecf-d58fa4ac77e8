import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rupify/models/expense.dart';
import 'package:rupify/providers/expense_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:rupify/providers/auth_provider.dart';
import 'package:rupify/screens/add_expense_screen.dart';
import 'package:rupify/utils/export_service.dart';
import 'package:intl/intl.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class ExpenseListScreen extends StatefulWidget {
  const ExpenseListScreen({super.key});

  @override
  State<ExpenseListScreen> createState() => _ExpenseListScreenState();
}

class _ExpenseListScreenState extends State<ExpenseListScreen> {
  // New filtering state variables
  int _selectedYear = DateTime.now().year;
  int _selectedMonth = DateTime.now().month;
  DateTime? _selectedDate;
  String _filterType = 'month'; // 'year', 'month', 'date'

  // Keep existing filter states
  String? _selectedCategory;
  bool? _selectedLendStatus;
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  void _loadBannerAd() {
    _bannerAd = BannerAd(
      adUnitId: 'ca-app-pub-8904820275824881/**********', // Test ad unit ID
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (_) {
          setState(() {
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
        },
      ),
    );

    _bannerAd?.load();
  }

  List<Expense> _getFilteredExpenses(ExpenseProvider provider) {
    List<Expense> expenses;

    // Use new granular filtering system
    switch (_filterType) {
      case 'year':
        expenses = provider.getExpensesByYear(_selectedYear);
        break;
      case 'month':
        expenses = provider.getExpensesByYearMonth(_selectedYear, _selectedMonth);
        break;
      case 'date':
        if (_selectedDate != null) {
          expenses = provider.getExpensesByDate(_selectedDate!);
        } else {
          expenses = provider.getExpensesByYearMonth(_selectedYear, _selectedMonth);
        }
        break;
      default:
        expenses = provider.getExpensesByYearMonth(_selectedYear, _selectedMonth);
    }

    // Filter by category if selected
    if (_selectedCategory != null) {
      expenses = expenses.where((e) => e.category.displayName == _selectedCategory).toList();
    }

    // Filter by lend status if selected
    if (_selectedLendStatus != null) {
      expenses = expenses.where((e) => e.isLend == _selectedLendStatus).toList();
    }

    return expenses;
  }

  @override
  Widget build(BuildContext context) {
    final expenseProvider = Provider.of<ExpenseProvider>(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    
    final filteredExpenses = _getFilteredExpenses(expenseProvider);
    
    // Sort expenses by date (newest first)
    filteredExpenses.sort((a, b) => b.date.compareTo(a.date));
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isHindi ? 'खर्च सूची' : 'Expense List'),
        actions: [
          IconButton(
            icon: const Icon(Icons.cloud_upload,color: Color(0xFF36916D),),
            onPressed: () {
              _uploadExpensesToServer(context);
            },
            tooltip: isHindi ? 'सर्वर पर अपलोड करें' : 'Upload to Server',
          ),
          IconButton(
            icon: const Icon(Icons.cloud_download,color: Color(0xFF36916D),),
            onPressed: () {
              _fetchExpensesFromServer(context);
            },
            tooltip: isHindi ? 'सर्वर से प्राप्त करें' : 'Fetch from Server',
          ),
          IconButton(
            icon: const Icon(Icons.file_download,color: Color(0xFF36916D),),
            onPressed: () {
              ExportService().exportToCsv(filteredExpenses);
            },
            tooltip: isHindi ? 'CSV निर्यात करें' : 'Export CSV',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter options
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isHindi ? 'फ़िल्टर करें' : 'Filter by Time Period',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                // Filter type selection
                Row(
                  children: [
                    _buildFilterTypeChip(
                      isHindi ? 'वर्ष' : 'Year',
                      'year',
                    ),
                    const SizedBox(width: 8),
                    _buildFilterTypeChip(
                      isHindi ? 'महीना' : 'Month',
                      'month',
                    ),
                    const SizedBox(width: 8),
                    _buildFilterTypeChip(
                      isHindi ? 'दिनांक' : 'Date',
                      'date',
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Date/Time pickers based on filter type
                _buildDateTimePickers(isHindi),
                const SizedBox(height: 16),
                Text(
                  isHindi ? 'श्रेणी द्वारा फ़िल्टर करें' : 'Filter by Category',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 50,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: FilterChip(
                          label: Text(isHindi ? 'सभी' : 'All'),
                          selected: _selectedCategory == null,
                          onSelected: (selected) {
                            setState(() {
                              _selectedCategory = null;
                            });
                          },
                        ),
                      ),
                      ...ExpenseCategory.values.map((category) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: FilterChip(
                            label: Text(
                              isHindi ? category.hindiName : category.displayName,
                            ),
                            selected: _selectedCategory == category.displayName,
                            avatar: Icon(
                              category.icon,
                              color: category.color,
                              size: 18,
                            ),
                            onSelected: (selected) {
                              setState(() {
                                _selectedCategory = selected ? category.displayName : null;
                              });
                            },
                          ),
                        );
                      }),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  isHindi ? 'उधार स्थिति द्वारा फ़िल्टर करें' : 'Filter by Lend Status',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildLendFilterChip(
                      isHindi ? 'सभी' : 'All',
                      null,
                    ),
                    const SizedBox(width: 8),
                    _buildLendFilterChip(
                      isHindi ? 'उधार' : 'Lends',
                      true,
                    ),
                    const SizedBox(width: 8),
                    _buildLendFilterChip(
                      isHindi ? 'खर्च' : 'Expenses',
                      false,
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Expense list
          Expanded(
            child: filteredExpenses.isEmpty
                ? Center(
                    child: Text(
                      isHindi ? 'कोई खर्च नहीं मिला' : 'No expenses found',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  )
                : ListView.builder(
                    itemCount: filteredExpenses.length,
                    itemBuilder: (context, index) {
                      final expense = filteredExpenses[index];
                      return Dismissible(
                        key: Key(expense.id),
                        direction: DismissDirection.endToStart,
                        background: Container(
                          color: Colors.red,
                          alignment: Alignment.centerRight,
                          padding: const EdgeInsets.only(right: 20.0),
                          child: const Icon(
                            Icons.delete,
                            color: Colors.white,
                          ),
                        ),
                        confirmDismiss: (direction) async {
                          return await showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                title: Text(isHindi ? 'पुष्टि करें' : 'Confirm'),
                                content: Text(
                                  isHindi
                                      ? 'क्या आप वाकई इस खर्च को हटाना चाहते हैं?'
                                      : 'Do you really want to delete this expense?',
                                ),
                                actions: <Widget>[
                                  TextButton(
                                    onPressed: () => Navigator.of(context).pop(false),
                                    child: Text(isHindi ? 'रद्द करें' : 'Cancel'),
                                  ),
                                  TextButton(
                                    onPressed: () => Navigator.of(context).pop(true),
                                    child: Text(isHindi ? 'हटाएं' : 'Delete'),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                        onDismissed: (direction) {
                          expenseProvider.deleteExpense(expense.id);
                        },
                        child: Card(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16.0,
                            vertical: 4.0,
                          ),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: expense.category.color.withValues(alpha: 0.2),
                              child: Icon(
                                expense.category.icon,
                                color: expense.category.color,
                              ),
                            ),
                            title: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    expense.description,
                                    style: const TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                                if (expense.isLend)
                                  Tooltip(
                                    message: isHindi ? 'उधार' : 'Lend',
                                    child: Icon(
                                      Icons.account_balance_wallet,
                                      size: 16,
                                      color: Colors.orange,
                                    ),
                                  ),
                              ],
                            ),
                            subtitle: Text(
                              '${DateFormat('dd MMM yyyy').format(expense.date)} • '
                              '${isHindi ? expense.category.hindiName : expense.category.displayName}',
                            ),
                            trailing: Text(
                              expenseProvider.formatCurrency(expense.amount),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (_) => AddExpenseScreen(expense: expense),
                                ),
                              );
                            },
                          ),
                        ),
                      );
                    },
                  ),
          ),
          
          // Banner Ad
          if (_isAdLoaded)
            SizedBox(
              height: _bannerAd!.size.height.toDouble(),
              width: _bannerAd!.size.width.toDouble(),
              child: AdWidget(ad: _bannerAd!),
            ),
        ],
      ),
    );
  }

  Widget _buildFilterTypeChip(String label, String filterType) {
    return FilterChip(
      label: Text(label),
      selected: _filterType == filterType,
      onSelected: (selected) {
        setState(() {
          _filterType = filterType;
          // Reset date selection when changing filter type
          if (filterType != 'date') {
            _selectedDate = null;
          }
        });
      },
    );
  }

  Widget _buildDateTimePickers(bool isHindi) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Year picker (always shown)
        Row(
          children: [
            Text(
              isHindi ? 'वर्ष: ' : 'Year: ',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(width: 8),
            DropdownButton<int>(
              value: _selectedYear,
              items: List.generate(10, (index) {
                final year = DateTime.now().year - index;
                return DropdownMenuItem(
                  value: year,
                  child: Text(year.toString()),
                );
              }),
              onChanged: (year) {
                if (year != null) {
                  setState(() {
                    _selectedYear = year;
                    // Reset month and date if they're invalid for the new year
                    if (_selectedMonth > 12) _selectedMonth = 12;
                    _selectedDate = null;
                  });
                }
              },
            ),
          ],
        ),

        // Month picker (shown for month and date filters)
        if (_filterType == 'month' || _filterType == 'date') ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                isHindi ? 'महीना: ' : 'Month: ',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(width: 8),
              DropdownButton<int>(
                value: _selectedMonth,
                items: List.generate(12, (index) {
                  final month = index + 1;
                  final monthName = DateFormat.MMMM().format(DateTime(2024, month));
                  return DropdownMenuItem(
                    value: month,
                    child: Text(monthName),
                  );
                }),
                onChanged: (month) {
                  if (month != null) {
                    setState(() {
                      _selectedMonth = month;
                      _selectedDate = null; // Reset date when month changes
                    });
                  }
                },
              ),
            ],
          ),
        ],

        // Date picker (shown only for date filter)
        if (_filterType == 'date') ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                isHindi ? 'दिनांक: ' : 'Date: ',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _selectedDate ?? DateTime(_selectedYear, _selectedMonth),
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now(),
                  );
                  if (date != null) {
                    setState(() {
                      _selectedDate = date;
                      _selectedYear = date.year;
                      _selectedMonth = date.month;
                    });
                  }
                },
                child: Text(
                  _selectedDate != null
                      ? DateFormat('dd/MM/yyyy').format(_selectedDate!)
                      : (isHindi ? 'दिनांक चुनें' : 'Select Date'),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildLendFilterChip(String label, bool? lendStatus) {
    return FilterChip(
      label: Text(label),
      selected: _selectedLendStatus == lendStatus,
      onSelected: (selected) {
        setState(() {
          _selectedLendStatus = selected ? lendStatus : null;
        });
      },
    );
  }

  Future<void> _uploadExpensesToServer(BuildContext context) async {
    final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    
    // Check if user is logged in
    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isHindi ? 'अपलोड करने के लिए लॉगिन करें' : 'Please login to upload expenses'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 20),
            Text(isHindi ? 'अपलोड हो रहा है...' : 'Uploading...'),
          ],
        ),
      ),
    );
    
    try {
      final result = await expenseProvider.uploadExpensesToServer(authProvider.token);
      
      // Close loading dialog
      Navigator.pop(context);
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isHindi 
            ? 'सफलतापूर्वक अपलोड किया गया: ${result['count']} खर्च' 
            : 'Successfully uploaded: ${result['count']} expenses'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Close loading dialog
      Navigator.pop(context);
      
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isHindi 
            ? 'अपलोड विफल: ${e.toString()}' 
            : 'Upload failed: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _fetchExpensesFromServer(BuildContext context) async {
    final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final isHindi = settingsProvider.isHindi;
    
    // Check if user is logged in
    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isHindi ? 'डेटा प्राप्त करने के लिए लॉगिन करें' : 'Please login to fetch data'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 20),
            Text(isHindi ? 'डेटा प्राप्त हो रहा है...' : 'Fetching data...'),
          ],
        ),
      ),
    );
    
    try {
      final result = await expenseProvider.fetchExpensesFromServer(authProvider.token);
      
      // Close loading dialog
      Navigator.pop(context);
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isHindi 
            ? 'सफलतापूर्वक प्राप्त किया गया: ${result['count']} खर्च' 
            : 'Successfully fetched: ${result['count']} expenses'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Close loading dialog
      Navigator.pop(context);
      
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isHindi 
            ? 'डेटा प्राप्त करने में विफल: ${e.toString()}' 
            : 'Fetch failed: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
