# Notification System Testing Guide

## Overview
This guide helps you test the comprehensive todo notification system that has been implemented. The system includes automatic language detection, immediate confirmation notifications, and multiple reminder notifications.

## Features Implemented

### 1. Automatic Language Detection
- App automatically detects device language on first launch
- Supports Hindi and English
- Notifications are shown in the detected language

### 2. Notification Types
- **Immediate Confirmation**: Shown immediately when a todo is scheduled
- **15-minute Reminder**: Shown 15 minutes before the scheduled time (if applicable)
- **5-minute Reminder**: Shown 5 minutes before the scheduled time (if applicable)
- **Exact Time Notification**: Shown at the exact scheduled time

### 3. Background Support
- Notifications work when app is closed
- Notifications work when device is locked
- Proper Android permissions and channels configured

## Testing Steps

### Test 1: Immediate Confirmation Notification
1. Open the app
2. Create a new todo with any future time
3. **Expected Result**: You should immediately see a confirmation notification saying "Todo Scheduled" (or "कार्य निर्धारित" in Hindi)

### Test 2: Near-Future Todo (Immediate Reminder)
1. Create a todo scheduled for 2-3 minutes from now
2. **Expected Result**: 
   - Immediate confirmation notification
   - At the scheduled time, you should get the reminder notification

### Test 3: Future Todo with Multiple Reminders
1. Create a todo scheduled for 20+ minutes from now
2. **Expected Results**:
   - Immediate confirmation notification
   - 15-minute reminder notification
   - 5-minute reminder notification  
   - Exact time notification

### Test 4: Background Notifications
1. Create a todo for 5 minutes from now
2. Close the app completely (swipe away from recent apps)
3. Wait for the scheduled time
4. **Expected Result**: Notification should still appear even with app closed

### Test 5: Language Detectiongitm
1. Change your device language to Hindi (if not already)
2. Clear app data or reinstall the app
3. Open the app
4. Create a todo
5. **Expected Result**: Notifications should appear in Hindi

### Test 6: Device States
1. Create a todo for 2 minutes from now
2. Lock your device
3. Wait for notification time
4. **Expected Result**: Notification should appear on lock screen

### Test 7: Notification Cancellation
1. Create a todo for future time
2. Delete the todo from the app
3. **Expected Result**: Scheduled notifications should be cancelled

## Troubleshooting

### If Notifications Don't Appear:
1. Check notification permissions in device settings
2. Ensure "Exact Alarms" permission is granted (Android 12+)
3. Check if battery optimization is disabled for the app
4. Verify the app has notification channels enabled

### If Language Detection Doesn't Work:
1. Clear app data and restart
2. Check device language settings
3. The app defaults to English if detection fails

### If Background Notifications Don't Work:
1. Disable battery optimization for the app
2. Check "Allow background activity" in app settings
3. Ensure "Exact Alarms" permission is granted

## Technical Details

### Notification Channels:
- **todo_channel**: High priority for reminders
- **todo_confirmation_channel**: Normal priority for confirmations

### Notification IDs:
- Base ID: Todo ID
- Confirmation: Base ID × 100
- 15-min reminder: Base ID × 15
- 5-min reminder: Base ID × 5

### Permissions Required:
- POST_NOTIFICATIONS
- SCHEDULE_EXACT_ALARM
- USE_EXACT_ALARM
- RECEIVE_BOOT_COMPLETED
- WAKE_LOCK

## Expected Behavior Summary

1. **Immediate**: Confirmation notification when todo is created
2. **15 minutes before**: Reminder notification (if todo is >15 min away)
3. **5 minutes before**: Reminder notification (if todo is >5 min away)
4. **Exact time**: Main reminder notification
5. **Language**: All notifications in detected device language
6. **Background**: All notifications work when app is closed/locked

## Notes
- The system uses Flutter Local Notifications (no Firebase)
- Timezone handling is automatic
- All notifications include proper styling and priority
- The system is optimized for battery efficiency
