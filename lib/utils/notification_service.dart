import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:shared_preferences/shared_preferences.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = 
      FlutterLocalNotificationsPlugin();
  
  Future<void> init() async {
    // Initialize timezone data
    tz_data.initializeTimeZones();

    // Set local timezone
    final String timeZoneName = await _getLocalTimeZone();
    tz.setLocalLocation(tz.getLocation(timeZoneName));

    // Request permissions for iOS and Android
    await flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()?.requestPermissions(
      alert: true,
      badge: true,
      sound: true,
    );

    // Initialize local notifications
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle notification tap
        _handleNotificationTap(response);
      },
    );

    // Create the notification channel for Android
    final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
        flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlugin != null) {
      await androidPlugin.requestNotificationsPermission();

      // Create high priority channel for todo reminders
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          'todo_channel',
          'Todo Reminders',
          description: 'Notifications for todo reminders',
          importance: Importance.max,
          enableLights: true,
          enableVibration: true,
          playSound: true,
          ledColor: Color(0xFF36916D),
          showBadge: true,
        ),
      );

      // Create channel for immediate confirmations
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          'todo_confirmation_channel',
          'Todo Confirmations',
          description: 'Immediate confirmations when todos are scheduled',
          importance: Importance.high,
          enableLights: true,
          enableVibration: true,
          playSound: true,
          ledColor: Color(0xFF36916D),
          showBadge: true,
        ),
      );
    }
  }

  void _handleNotificationTap(NotificationResponse response) {
    // Navigate to todo screen
    // We'll use payload to store the route
    if (response.payload != null) {
      // The navigation will be handled in main.dart with a GlobalKey<NavigatorState>
    }
  }

  Future<String> _getLocalTimeZone() async {
    try {
      // Try to get saved timezone from preferences
      final prefs = await SharedPreferences.getInstance();
      final savedTimeZone = prefs.getString('timezone');

      if (savedTimeZone != null) {
        return savedTimeZone;
      }

      // Default to common timezones based on likely usage
      // You can expand this logic based on device locale
      return 'Asia/Kolkata'; // Default for Indian users
    } catch (e) {
      return 'UTC'; // Fallback
    }
  }

  Future<void> scheduleTodoNotification(
    String id,
    String title,
    String body,
    DateTime scheduledDate,
    TimeOfDay scheduledTime,
  ) async {
    // Get language preference for notifications
    final prefs = await SharedPreferences.getInstance();
    final isHindi = prefs.getBool('isHindi') ?? false;

    // Calculate the todo time
    final DateTime todoDateTime = DateTime(
      scheduledDate.year,
      scheduledDate.month,
      scheduledDate.day,
      scheduledTime.hour,
      scheduledTime.minute,
    );

    // Create notification details for confirmations
    final NotificationDetails confirmationDetails = NotificationDetails(
      android: AndroidNotificationDetails(
        'todo_confirmation_channel',
        'Todo Confirmations',
        channelDescription: 'Immediate confirmations when todos are scheduled',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
        color: const Color(0xFF36916D),
        enableLights: true,
        playSound: true,
        ledColor: const Color(0xFF36916D),
        styleInformation: BigTextStyleInformation(
          isHindi ? 'कार्य "$title" समय पर निर्धारित किया गया' : 'Task "$title" scheduled successfully',
          contentTitle: isHindi ? 'कार्य निर्धारित' : 'Todo Scheduled',
          summaryText: isHindi ? 'कार्य पुष्टि' : 'Task Confirmation',
        ),
      ),
      iOS: const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'default',
      ),
    );

    // Create notification details for reminders
    final NotificationDetails reminderDetails = NotificationDetails(
      android: AndroidNotificationDetails(
        'todo_channel',
        'Todo Reminders',
        channelDescription: 'Notifications for todo reminders',
        importance: Importance.max,
        priority: Priority.max,
        icon: '@mipmap/ic_launcher',
        largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        color: const Color(0xFF36916D),
        enableLights: true,
        playSound: true,
        ledColor: const Color(0xFF36916D),
        ledOnMs: 1000,
        ledOffMs: 500,
        styleInformation: BigTextStyleInformation(
          body.isEmpty ? (isHindi ? 'आपका कार्य पूरा करने का समय है!' : 'Time to complete your todo!') : body,
          contentTitle: title,
          summaryText: isHindi ? 'कार्य अनुस्मारक' : 'Todo Reminder',
        ),
      ),
      iOS: const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'default',
        badgeNumber: 1,
      ),
    );

    try {
      // 1. Always show immediate confirmation notification
      final formattedTime = '${scheduledTime.hour.toString().padLeft(2, '0')}:${scheduledTime.minute.toString().padLeft(2, '0')}';
      await flutterLocalNotificationsPlugin.show(
        int.parse(id) * 100, // Unique ID for confirmation
        isHindi ? 'कार्य निर्धारित' : 'Todo Scheduled',
        isHindi ? 'कार्य "$title" $formattedTime के लिए निर्धारित' : 'Task "$title" scheduled for $formattedTime',
        confirmationDetails,
      );
    } catch (e) {
      debugPrint('Error showing confirmation notification: $e');
    }

    // 2. Schedule reminder notifications if todo is in the future
    if (todoDateTime.isAfter(DateTime.now())) {
      await _scheduleReminderNotifications(id, title, body, todoDateTime, isHindi, reminderDetails);
    } else {
      // 3. If todo is in the past or very soon, show immediate reminder
      try {
        await flutterLocalNotificationsPlugin.show(
          int.parse(id),
          title,
          isHindi ? 'यह कार्य अभी देय है!' : 'This todo is due now!',
          reminderDetails,
        );
      } catch (e) {
        debugPrint('Error showing immediate reminder: $e');
      }
    }
  }

  Future<void> _scheduleReminderNotifications(
    String id,
    String title,
    String body,
    DateTime todoDateTime,
    bool isHindi,
    NotificationDetails reminderDetails,
  ) async {
    final now = DateTime.now();
    final baseId = int.parse(id);

    try {
      // Schedule 15-minute reminder if todo is more than 15 minutes away
      final fifteenMinBefore = todoDateTime.subtract(const Duration(minutes: 15));
      if (fifteenMinBefore.isAfter(now)) {
        final scheduledTime = tz.TZDateTime.from(fifteenMinBefore, tz.local);
        await flutterLocalNotificationsPlugin.zonedSchedule(
          baseId * 15, // Unique ID for 15-min reminder
          title,
          isHindi ? 'आपका कार्य 15 मिनट में शुरू होगा' : 'Your todo starts in 15 minutes',
          scheduledTime,
          reminderDetails,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        );
      }

      // Schedule 5-minute reminder if todo is more than 5 minutes away
      final fiveMinBefore = todoDateTime.subtract(const Duration(minutes: 5));
      if (fiveMinBefore.isAfter(now)) {
        final scheduledTime = tz.TZDateTime.from(fiveMinBefore, tz.local);
        await flutterLocalNotificationsPlugin.zonedSchedule(
          baseId * 5, // Unique ID for 5-min reminder
          title,
          isHindi ? 'आपका कार्य 5 मिनट में शुरू होगा' : 'Your todo starts in 5 minutes',
          scheduledTime,
          reminderDetails,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        );
      }

      // Schedule exact time notification
      final scheduledTime = tz.TZDateTime.from(todoDateTime, tz.local);
      await flutterLocalNotificationsPlugin.zonedSchedule(
        baseId, // Main notification ID
        title,
        body.isEmpty ? (isHindi ? 'आपका कार्य पूरा करने का समय है!' : 'Time to complete your todo!') : body,
        scheduledTime,
        reminderDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      );

    } catch (e) {
      debugPrint('Error scheduling reminder notifications: $e');
    }
  }

  Future<void> cancelNotification(String id) async {
    // Cancel all notifications (confirmation, 15-min, 5-min, exact time)
    final int baseId = int.parse(id);
    final int confirmationId = baseId * 100;
    final int fifteenMinId = baseId * 15;
    final int fiveMinId = baseId * 5;

    await flutterLocalNotificationsPlugin.cancel(confirmationId);
    await flutterLocalNotificationsPlugin.cancel(fifteenMinId);
    await flutterLocalNotificationsPlugin.cancel(fiveMinId);
    await flutterLocalNotificationsPlugin.cancel(baseId);
  }

}
