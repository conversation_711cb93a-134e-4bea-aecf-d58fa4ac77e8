import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:rupify/models/expense.dart';
import 'package:rupify/providers/expense_provider.dart';
import 'package:rupify/providers/settings_provider.dart';
import 'package:intl/intl.dart';

class AnalyticsScreen extends StatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen> {
  // New filtering state variables
  int _selectedYear = DateTime.now().year;
  int _selectedMonth = DateTime.now().month;
  DateTime? _selectedDate;
  String _filterType = 'month'; // 'year', 'month', 'date'

  List<Expense> _getFilteredExpenses(ExpenseProvider provider) {
    switch (_filterType) {
      case 'year':
        return provider.getExpensesByYear(_selectedYear);
      case 'month':
        return provider.getExpensesByYearMonth(_selectedYear, _selectedMonth);
      case 'date':
        if (_selectedDate != null) {
          return provider.getExpensesByDate(_selectedDate!);
        } else {
          return provider.getExpensesByYearMonth(_selectedYear, _selectedMonth);
        }
      default:
        return provider.getExpensesByYearMonth(_selectedYear, _selectedMonth);
    }
  }

  @override
  Widget build(BuildContext context) {
    final expenseProvider = Provider.of<ExpenseProvider>(context);
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final isHindi = settingsProvider.isHindi;
    
    final filteredExpenses = _getFilteredExpenses(expenseProvider);
    final categoryTotals = expenseProvider.getCategoryTotals(filteredExpenses);
    
    // Sort categories by amount (highest first)
    final sortedCategories = categoryTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    final totalSpent = categoryTotals.values.fold(0.0, (sum, amount) => sum + amount);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isHindi ? 'विश्लेषण' : 'Analytics'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Time period filter
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isHindi ? 'समय अवधि फ़िल्टर' : 'Time Period Filter',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 12),
                    // Filter type selection
                    Row(
                      children: [
                        _buildFilterTypeChip(
                          isHindi ? 'वर्ष' : 'Year',
                          'year',
                        ),
                        const SizedBox(width: 8),
                        _buildFilterTypeChip(
                          isHindi ? 'महीना' : 'Month',
                          'month',
                        ),
                        const SizedBox(width: 8),
                        _buildFilterTypeChip(
                          isHindi ? 'दिनांक' : 'Date',
                          'date',
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    // Date/Time pickers based on filter type
                    _buildDateTimePickers(isHindi),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Total spent
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isHindi ? 'कुल खर्च' : 'Total Spent',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      expenseProvider.formatCurrency(totalSpent),
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade800,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getTimeRangeText(isHindi),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Pie chart
            if (totalSpent > 0) ...[
              Text(
                isHindi ? 'श्रेणी वार खर्च' : 'Spending by Category',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 250,
                child: PieChart(
                  PieChartData(
                    sections: _buildPieChartSections(sortedCategories, totalSpent),
                    centerSpaceRadius: 40,
                    sectionsSpace: 2,
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Category breakdown
              Text(
                isHindi ? 'श्रेणी विवरण' : 'Category Breakdown',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              ...sortedCategories.map((entry) {
                final category = entry.key;
                final amount = entry.value;
                final percentage = (amount / totalSpent * 100).toStringAsFixed(1);
                
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: category.color,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          category.icon,
                          color: Colors.white,
                          size: 14,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          isHindi ? category.hindiName : category.displayName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Text(
                        expenseProvider.formatCurrency(amount),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        width: 45,
                        child: Text(
                          '$percentage%',
                          textAlign: TextAlign.right,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ] else ...[
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.bar_chart,
                        size: 64,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        isHindi
                            ? 'इस अवधि के लिए कोई खर्च नहीं मिला'
                            : 'No expenses found for this period',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFilterTypeChip(String label, String filterType) {
    return FilterChip(
      label: Text(label),
      selected: _filterType == filterType,
      onSelected: (selected) {
        setState(() {
          _filterType = filterType;
          // Reset date selection when changing filter type
          if (filterType != 'date') {
            _selectedDate = null;
          }
        });
      },
    );
  }

  Widget _buildDateTimePickers(bool isHindi) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Year picker (always shown)
        Row(
          children: [
            Text(
              isHindi ? 'वर्ष: ' : 'Year: ',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(width: 8),
            DropdownButton<int>(
              value: _selectedYear,
              items: List.generate(10, (index) {
                final year = DateTime.now().year - index;
                return DropdownMenuItem(
                  value: year,
                  child: Text(year.toString()),
                );
              }),
              onChanged: (year) {
                if (year != null) {
                  setState(() {
                    _selectedYear = year;
                    // Reset month and date if they're invalid for the new year
                    if (_selectedMonth > 12) _selectedMonth = 12;
                    _selectedDate = null;
                  });
                }
              },
            ),
          ],
        ),

        // Month picker (shown for month and date filters)
        if (_filterType == 'month' || _filterType == 'date') ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                isHindi ? 'महीना: ' : 'Month: ',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(width: 8),
              DropdownButton<int>(
                value: _selectedMonth,
                items: List.generate(12, (index) {
                  final month = index + 1;
                  final monthName = DateFormat.MMMM().format(DateTime(2024, month));
                  return DropdownMenuItem(
                    value: month,
                    child: Text(monthName),
                  );
                }),
                onChanged: (month) {
                  if (month != null) {
                    setState(() {
                      _selectedMonth = month;
                      _selectedDate = null; // Reset date when month changes
                    });
                  }
                },
              ),
            ],
          ),
        ],

        // Date picker (shown only for date filter)
        if (_filterType == 'date') ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                isHindi ? 'दिनांक: ' : 'Date: ',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _selectedDate ?? DateTime(_selectedYear, _selectedMonth),
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now(),
                  );
                  if (date != null) {
                    setState(() {
                      _selectedDate = date;
                      _selectedYear = date.year;
                      _selectedMonth = date.month;
                    });
                  }
                },
                child: Text(
                  _selectedDate != null
                      ? DateFormat('dd/MM/yyyy').format(_selectedDate!)
                      : (isHindi ? 'दिनांक चुनें' : 'Select Date'),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  List<PieChartSectionData> _buildPieChartSections(
    List<MapEntry<ExpenseCategory, double>> sortedCategories,
    double totalSpent,
  ) {
    return sortedCategories.map((entry) {
      final category = entry.key;
      final amount = entry.value;
      final percentage = amount / totalSpent;
      
      return PieChartSectionData(
        color: category.color,
        value: amount,
        title: '${(percentage * 100).toStringAsFixed(1)}%',
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  String _getTimeRangeText(bool isHindi) {
    switch (_filterType) {
      case 'year':
        return _selectedYear.toString();
      case 'month':
        final date = DateTime(_selectedYear, _selectedMonth);
        return isHindi
          ? DateFormat('MMMM yyyy', 'hi').format(date)
          : DateFormat('MMMM yyyy').format(date);
      case 'date':
        if (_selectedDate != null) {
          return isHindi
            ? DateFormat('dd MMMM yyyy', 'hi').format(_selectedDate!)
            : DateFormat('MMMM dd, yyyy').format(_selectedDate!);
        } else {
          final date = DateTime(_selectedYear, _selectedMonth);
          return isHindi
            ? DateFormat('MMMM yyyy', 'hi').format(date)
            : DateFormat('MMMM yyyy').format(date);
        }
      default:
        return '';
    }
  }
}
