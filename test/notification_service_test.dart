import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rupify/utils/notification_service.dart';

void main() {
  // Initialize Flutter binding for tests
  TestWidgetsFlutterBinding.ensureInitialized();

  group('NotificationService Tests', () {
    late NotificationService notificationService;

    setUp(() {
      notificationService = NotificationService();
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({
        'isHindi': false,
        'hasDetectedLanguage': false,
      });
    });

    test('should initialize notification service without errors', () async {
      // This test verifies that the notification service can be initialized
      // In a real test environment, we would mock the platform-specific calls
      expect(notificationService, isNotNull);
    });

    test('should schedule todo notification with proper parameters', () async {
      // Test scheduling a notification
      final testId = '123';
      final testTitle = 'Test Todo';
      final testBody = 'This is a test todo';
      final testDate = DateTime.now().add(const Duration(hours: 1));
      final testTime = TimeOfDay(hour: testDate.hour, minute: testDate.minute);

      // In a real test, we would mock the flutter_local_notifications plugin
      // and verify that the correct methods are called with correct parameters
      expect(() async {
        await notificationService.scheduleTodoNotification(
          testId,
          testTitle,
          testBody,
          testDate,
          testTime,
        );
      }, returnsNormally);
    });

    test('should cancel notification with proper ID', () async {
      final testId = '123';
      
      // Test canceling a notification
      expect(() async {
        await notificationService.cancelNotification(testId);
      }, returnsNormally);
    });

    test('should handle Hindi language preference', () async {
      // Set Hindi preference
      SharedPreferences.setMockInitialValues({
        'isHindi': true,
        'hasDetectedLanguage': true,
      });

      final testId = '456';
      final testTitle = 'हिंदी टेस्ट';
      final testBody = 'यह एक हिंदी परीक्षण है';
      final testDate = DateTime.now().add(const Duration(hours: 2));
      final testTime = TimeOfDay(hour: testDate.hour, minute: testDate.minute);

      expect(() async {
        await notificationService.scheduleTodoNotification(
          testId,
          testTitle,
          testBody,
          testDate,
          testTime,
        );
      }, returnsNormally);
    });

    test('should handle immediate notifications for past dates', () async {
      final testId = '789';
      final testTitle = 'Past Todo';
      final testBody = 'This todo is in the past';
      final testDate = DateTime.now().subtract(const Duration(hours: 1));
      final testTime = TimeOfDay(hour: testDate.hour, minute: testDate.minute);

      expect(() async {
        await notificationService.scheduleTodoNotification(
          testId,
          testTitle,
          testBody,
          testDate,
          testTime,
        );
      }, returnsNormally);
    });

    test('should handle notifications for near-future dates', () async {
      final testId = '101';
      final testTitle = 'Near Future Todo';
      final testBody = 'This todo is very soon';
      final testDate = DateTime.now().add(const Duration(minutes: 3));
      final testTime = TimeOfDay(hour: testDate.hour, minute: testDate.minute);

      expect(() async {
        await notificationService.scheduleTodoNotification(
          testId,
          testTitle,
          testBody,
          testDate,
          testTime,
        );
      }, returnsNormally);
    });
  });

  group('Notification Scheduling Logic Tests', () {
    test('should calculate correct reminder times', () {
      final todoDateTime = DateTime(2024, 1, 1, 15, 30); // 3:30 PM
      final now = DateTime(2024, 1, 1, 14, 0); // 2:00 PM
      
      // 15 minutes before should be 3:15 PM
      final fifteenMinBefore = todoDateTime.subtract(const Duration(minutes: 15));
      expect(fifteenMinBefore.hour, equals(15));
      expect(fifteenMinBefore.minute, equals(15));
      expect(fifteenMinBefore.isAfter(now), isTrue);
      
      // 5 minutes before should be 3:25 PM
      final fiveMinBefore = todoDateTime.subtract(const Duration(minutes: 5));
      expect(fiveMinBefore.hour, equals(15));
      expect(fiveMinBefore.minute, equals(25));
      expect(fiveMinBefore.isAfter(now), isTrue);
    });

    test('should handle edge cases for reminder scheduling', () {
      // Test when todo is less than 15 minutes away
      final todoDateTime = DateTime(2024, 1, 1, 14, 10); // 2:10 PM
      final now = DateTime(2024, 1, 1, 14, 0); // 2:00 PM
      
      final fifteenMinBefore = todoDateTime.subtract(const Duration(minutes: 15));
      expect(fifteenMinBefore.isBefore(now), isTrue); // Should not schedule 15-min reminder
      
      final fiveMinBefore = todoDateTime.subtract(const Duration(minutes: 5));
      expect(fiveMinBefore.isAfter(now), isTrue); // Should schedule 5-min reminder
    });
  });
}
